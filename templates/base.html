<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Garden Planner{% endblock %}</title>
    <!-- Favicon using plant emoji as SVG -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🌱</text></svg>">
    <!-- Material 3 Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap" rel="stylesheet">
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/style.css">
    <link rel="stylesheet" href="/static/css/wizard.css">
    <script src="https://unpkg.com/htmx.org@1.9.2"></script>
    <script src="/static/js/scripts.js" defer></script>
    {% block head %}{% endblock %}
</head>
<body class="bg-surface-50 dark:bg-surface-950 text-surface-900 dark:text-surface-100 transition-colors duration-200 font-sans min-h-screen">
    <!-- Navigation -->
    <nav class="bg-primary-700 dark:bg-primary-800 text-white shadow-elevation-2">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="/" class="text-title-large font-medium text-white hover:text-primary-200 transition-colors flex items-center gap-2">
                        <span class="material-icons text-2xl">eco</span>
                        Garden Planner
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-6">
                    {% if user_context.is_authenticated %}
                        <!-- Main Navigation Links -->
                        <div class="hidden lg:flex items-center space-x-2 xl:space-x-4 overflow-hidden">
                            <a href="/" class="text-body-large font-medium text-white hover:text-primary-200 transition-colors px-4 py-3 rounded-lg whitespace-nowrap min-h-[48px] flex items-center">Dashboard</a>
                            <a href="/plants/list" class="text-body-large font-medium text-white hover:text-primary-200 transition-colors px-4 py-3 rounded-lg whitespace-nowrap min-h-[48px] flex items-center">Plants</a>
                            <a href="/seeds/list" class="text-body-large font-medium text-white hover:text-primary-200 transition-colors px-4 py-3 rounded-lg whitespace-nowrap min-h-[48px] flex items-center">Seeds</a>
                            <a href="/property" class="text-body-large font-medium text-white hover:text-primary-200 transition-colors px-4 py-3 rounded-lg whitespace-nowrap min-h-[48px] flex items-center">Properties</a>
                            <a href="/seasons/list" class="text-body-large font-medium text-white hover:text-primary-200 transition-colors px-4 py-3 rounded-lg whitespace-nowrap min-h-[48px] flex items-center">Seasons</a>
                            <a href="/season_plans" class="text-body-large font-medium text-white hover:text-primary-200 transition-colors px-4 py-3 rounded-lg whitespace-nowrap min-h-[48px] flex items-center">Plans</a>
                            <a href="/wishlist/plants" class="text-body-large font-medium text-white hover:text-primary-200 transition-colors px-4 py-3 rounded-lg whitespace-nowrap min-h-[48px] flex items-center">Wishlist</a>
                            {% if user_context.is_admin %}
                                <a href="/admin" class="text-body-large font-medium text-white hover:text-primary-200 transition-colors px-4 py-3 rounded-lg whitespace-nowrap min-h-[48px] flex items-center">Admin</a>
                            {% endif %}
                        </div>

                        <!-- Notifications -->
                        <div class="relative" id="notifications-container">
                            <button id="notifications-button" class="text-white hover:text-primary-200 p-3 rounded-lg transition-colors relative min-h-[48px] min-w-[48px] flex items-center justify-center">
                                <span class="material-icons text-xl">notifications</span>
                                <!-- Notification badge -->
                                <span id="notification-badge" class="absolute -top-1 -right-1 bg-error-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center hidden shadow-elevation-2">0</span>
                            </button>
                            <!-- Notifications Dropdown -->
                            <div id="notifications-menu" class="absolute right-0 top-full mt-2 w-80 bg-surface-50 dark:bg-surface-800 rounded-lg shadow-elevation-3 py-1 z-50 hidden border border-surface-200 dark:border-surface-700 max-h-96 overflow-y-auto">
                                <div class="px-4 py-3 border-b border-surface-200 dark:border-surface-700">
                                    <h3 class="text-title-small font-medium text-surface-900 dark:text-surface-100">Notifications</h3>
                                </div>
                                <div id="notifications-list" class="py-1">
                                    <!-- Notifications will be loaded here -->
                                    <div class="px-4 py-3 text-body-medium text-surface-500 dark:text-surface-400 text-center">
                                        No new notifications
                                    </div>
                                </div>
                                <div class="border-t border-surface-200 dark:border-surface-700 px-4 py-2">
                                    <a href="/notifications" class="text-body-small text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300">View all notifications</a>
                                </div>
                            </div>
                        </div>

                        <!-- User Menu -->
                        <div class="relative" id="user-menu-container">
                            <button id="user-menu-button" class="flex items-center space-x-2 xl:space-x-3 text-white hover:text-primary-200 transition-colors px-3 py-3 rounded-lg min-h-[48px]">
                                <div class="w-8 h-8 xl:w-10 xl:h-10 bg-primary-600 rounded-full flex items-center justify-center text-label-medium xl:text-label-large font-medium relative shadow-elevation-1">
                                    <span class="material-icons text-base xl:text-lg text-white">eco</span>
                                </div>
                                <span class="hidden xl:block text-label-large max-w-24 truncate">{{ user_context.username }}</span>
                                <span class="material-icons text-base xl:text-lg">expand_more</span>
                            </button>
                            <!-- User Dropdown Menu -->
                            <div id="user-menu" class="absolute right-0 top-full mt-2 w-80 bg-white dark:bg-surface-800 rounded-lg shadow-elevation-3 py-2 z-50 hidden border border-surface-200 dark:border-surface-700 max-h-[32rem] overflow-y-auto">
                                <!-- User Info Section -->
                                <div class="px-4 py-3 border-b border-surface-200 dark:border-surface-700">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-12 h-12 bg-primary-600 rounded-full flex items-center justify-center text-white font-semibold shadow-elevation-1 flex-shrink-0">
                                            <span class="material-icons text-xl">eco</span>
                                        </div>
                                        <div class="min-w-0 flex-1">
                                            <p class="text-body-medium font-medium text-surface-900 dark:text-surface-100 truncate">{{ user_context.username }}</p>
                                            <p class="text-body-small text-surface-500 dark:text-surface-400">{{ user_context.role|title }}</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Household Switcher -->
                                <div class="px-4 py-3 border-b border-surface-200 dark:border-surface-700">
                                    <p class="text-label-small font-medium text-surface-500 dark:text-surface-400 uppercase tracking-wide mb-2">Current Household</p>
                                    {% if user_context.current_household_name %}
                                        <div class="flex items-center space-x-2 text-body-medium text-surface-700 dark:text-surface-200 mb-2">
                                            <span class="material-icons text-lg text-primary-500 flex-shrink-0">home</span>
                                            <span class="font-medium truncate">{{ user_context.current_household_name }}</span>
                                        </div>
                                    {% endif %}
                                    <a href="/households" class="flex items-center space-x-2 text-body-small text-surface-600 dark:text-surface-300 hover:bg-surface-100 dark:hover:bg-surface-700 rounded-md px-3 py-2 transition-colors w-full">
                                        <span class="material-icons text-base flex-shrink-0">swap_horiz</span>
                                        <span class="truncate">Switch Household</span>
                                    </a>
                                </div>

                                <!-- Menu Items -->
                                <div class="py-2">
                                    <a href="/profile" class="flex items-center px-5 py-4 text-body-large text-surface-700 dark:text-surface-200 hover:bg-surface-100 dark:hover:bg-surface-700 transition-colors w-full min-h-[48px]">
                                        <span class="material-icons text-xl mr-4 flex-shrink-0">person</span>
                                        <span class="truncate">Profile Settings</span>
                                    </a>
                                    <a href="/settings" class="flex items-center px-5 py-4 text-body-large text-surface-700 dark:text-surface-200 hover:bg-surface-100 dark:hover:bg-surface-700 transition-colors w-full min-h-[48px]">
                                        <span class="material-icons text-xl mr-4 flex-shrink-0">settings</span>
                                        <span class="truncate">Account Settings</span>
                                    </a>
                                    <a href="/households" class="flex items-center px-5 py-4 text-body-large text-surface-700 dark:text-surface-200 hover:bg-surface-100 dark:hover:bg-surface-700 transition-colors w-full min-h-[48px]">
                                        <span class="material-icons text-xl mr-4 flex-shrink-0">groups</span>
                                        <span class="truncate">Manage Households</span>
                                    </a>
                                    <button id="theme-toggle-menu" class="w-full flex items-center px-5 py-4 text-body-large text-surface-700 dark:text-surface-200 hover:bg-surface-100 dark:hover:bg-surface-700 transition-colors min-h-[48px]">
                                        <span id="theme-toggle-dark-icon-menu" class="material-icons text-xl mr-4 flex-shrink-0">dark_mode</span>
                                        <span id="theme-toggle-light-icon-menu" class="material-icons text-xl mr-4 flex-shrink-0 hidden">light_mode</span>
                                        <span id="theme-toggle-text" class="truncate">Dark Mode</span>
                                    </button>
                                </div>

                                <hr class="my-1 border-surface-200 dark:border-surface-700">

                                <div class="py-2">
                                    <a href="/auth/logout" class="flex items-center px-5 py-4 text-body-large text-surface-700 dark:text-surface-200 hover:bg-surface-100 dark:hover:bg-surface-700 transition-colors w-full min-h-[48px]">
                                        <span class="material-icons text-xl mr-4 flex-shrink-0">logout</span>
                                        <span class="truncate">Sign Out</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    {% else %}
                        <!-- Guest Navigation -->
                        <div class="flex items-center space-x-4">
                            <!-- Theme Toggle for guests -->
                            <button id="theme-toggle" type="button" class="text-white hover:text-primary-200 p-3 rounded-lg transition-colors min-h-[48px] min-w-[48px] flex items-center justify-center">
                                <span id="theme-toggle-dark-icon" class="material-icons text-lg">dark_mode</span>
                                <span id="theme-toggle-light-icon" class="material-icons text-lg hidden">light_mode</span>
                            </button>
                            <a href="/auth/register" class="text-label-large text-white hover:text-primary-200 transition-colors px-4 py-3 rounded-md min-h-[48px] flex items-center">Register</a>
                            <a href="/auth/login" class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-3 rounded-md transition-colors shadow-elevation-2 text-label-large font-medium min-h-[48px] flex items-center">Login</a>
                        </div>
                    {% endif %}
                </div>

                <!-- Mobile menu button -->
                <div class="lg:hidden">
                    <button id="mobile-menu-button" type="button" class="text-white hover:text-primary-200 p-3 rounded-lg transition-colors min-h-[48px] min-w-[48px] flex items-center justify-center">
                        <span class="material-icons text-xl">menu</span>
                    </button>
                </div>
            </div>

            <!-- Mobile Navigation Menu -->
            <div id="mobile-menu" class="lg:hidden hidden">
                <div class="px-4 pt-4 pb-3 space-y-2 border-t border-primary-600 max-h-96 overflow-y-auto">
                    {% if user_context.is_authenticated %}
                        <a href="/" class="flex items-center px-4 py-4 text-label-large text-white hover:text-primary-200 hover:bg-primary-600 transition-colors rounded-md min-h-[48px]">
                            <span class="material-icons text-lg mr-3">dashboard</span>
                            Dashboard
                        </a>
                        <a href="/plants/list" class="flex items-center px-4 py-4 text-label-large text-white hover:text-primary-200 hover:bg-primary-600 transition-colors rounded-md min-h-[48px]">
                            <span class="material-icons text-lg mr-3">local_florist</span>
                            Plants
                        </a>
                        <a href="/seeds/list" class="flex items-center px-4 py-4 text-label-large text-white hover:text-primary-200 hover:bg-primary-600 transition-colors rounded-md min-h-[48px]">
                            <span class="material-icons text-lg mr-3">inventory</span>
                            Seeds
                        </a>
                        <a href="/property" class="flex items-center px-4 py-4 text-label-large text-white hover:text-primary-200 hover:bg-primary-600 transition-colors rounded-md min-h-[48px]">
                            <span class="material-icons text-lg mr-3">home</span>
                            Properties
                        </a>
                        <a href="/seasons/list" class="flex items-center px-4 py-4 text-label-large text-white hover:text-primary-200 hover:bg-primary-600 transition-colors rounded-md min-h-[48px]">
                            <span class="material-icons text-lg mr-3">calendar_today</span>
                            Seasons
                        </a>
                        <a href="/season_plans" class="flex items-center px-4 py-4 text-label-large text-white hover:text-primary-200 hover:bg-primary-600 transition-colors rounded-md min-h-[48px]">
                            <span class="material-icons text-lg mr-3">event</span>
                            Plans
                        </a>
                        <a href="/wishlist/plants" class="flex items-center px-4 py-4 text-label-large text-white hover:text-primary-200 hover:bg-primary-600 transition-colors rounded-md min-h-[48px]">
                            <span class="material-icons text-lg mr-3">favorite</span>
                            Wishlist
                        </a>
                        {% if user_context.is_admin %}
                            <a href="/admin" class="flex items-center px-4 py-4 text-label-large text-white hover:text-primary-200 hover:bg-primary-600 transition-colors rounded-md min-h-[48px]">
                                <span class="material-icons text-lg mr-3">admin_panel_settings</span>
                                Admin
                            </a>
                        {% endif %}
                        <hr class="my-3 border-primary-600">
                        <a href="/profile" class="flex items-center px-4 py-4 text-label-large text-white hover:text-primary-200 hover:bg-primary-600 transition-colors rounded-md min-h-[48px]">
                            <span class="material-icons text-lg mr-3">person</span>
                            Profile
                        </a>
                        <a href="/settings" class="flex items-center px-4 py-4 text-label-large text-white hover:text-primary-200 hover:bg-primary-600 transition-colors rounded-md min-h-[48px]">
                            <span class="material-icons text-lg mr-3">settings</span>
                            Settings
                        </a>
                        <a href="/households" class="flex items-center px-4 py-4 text-label-large text-white hover:text-primary-200 hover:bg-primary-600 transition-colors rounded-md min-h-[48px]">
                            <span class="material-icons text-lg mr-3">groups</span>
                            Households
                        </a>
                        <a href="/auth/logout" class="flex items-center px-4 py-4 text-label-large text-white hover:text-primary-200 hover:bg-primary-600 transition-colors rounded-md min-h-[48px]">
                            <span class="material-icons text-lg mr-3">logout</span>
                            Logout
                        </a>
                    {% else %}
                        <a href="/auth/register" class="flex items-center px-4 py-4 text-label-large text-white hover:text-primary-200 hover:bg-primary-600 transition-colors rounded-md min-h-[48px]">
                            <span class="material-icons text-lg mr-3">person_add</span>
                            Register
                        </a>
                        <a href="/auth/login" class="flex items-center px-4 py-4 text-label-large text-white hover:text-primary-200 hover:bg-primary-600 transition-colors rounded-md min-h-[48px]">
                            <span class="material-icons text-lg mr-3">login</span>
                            Login
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </nav>

    <!-- Hidden div to ensure dark mode classes are generated -->
    <div class="hidden dark:bg-surface-950 dark:bg-surface-900 dark:bg-surface-800 dark:bg-surface-700 dark:bg-surface-600 dark:text-surface-100 dark:text-surface-200 dark:text-surface-300 dark:text-surface-400 dark:border-surface-700 dark:border-surface-600 dark:hover:bg-surface-700 dark:hover:text-surface-100 dark:text-primary-200 dark:text-primary-300 dark:text-primary-400 dark:bg-primary-800 dark:bg-primary-900 dark:text-secondary-200 dark:text-secondary-300 dark:text-secondary-400 dark:bg-secondary-900 dark:text-tertiary-200 dark:text-tertiary-300 dark:text-tertiary-400 dark:bg-tertiary-900 dark:text-error-200 dark:text-error-300 dark:text-error-400 dark:bg-error-900"></div>

<main class="container mx-auto px-6 py-8">
    {% block content %}{% endblock %}
</main>

<script>
// Enhanced navigation functionality
document.addEventListener('DOMContentLoaded', function() {
    // User menu dropdown functionality with hover support
    const userMenuButton = document.getElementById('user-menu-button');
    const userMenu = document.getElementById('user-menu');
    const userMenuContainer = document.getElementById('user-menu-container');
    let hoverTimeout;

    if (userMenuButton && userMenu && userMenuContainer) {
        // Click functionality (primary)
        userMenuButton.addEventListener('click', function(e) {
            e.stopPropagation();
            userMenu.classList.toggle('hidden');
        });

        // Hover functionality (secondary)
        userMenuContainer.addEventListener('mouseenter', function() {
            clearTimeout(hoverTimeout);
            userMenu.classList.remove('hidden');
        });

        userMenuContainer.addEventListener('mouseleave', function() {
            hoverTimeout = setTimeout(function() {
                userMenu.classList.add('hidden');
            }, 300); // 300ms delay before hiding
        });

        // Close menu when clicking outside
        document.addEventListener('click', function() {
            userMenu.classList.add('hidden');
        });

        // Prevent menu from closing when clicking inside it
        userMenu.addEventListener('click', function(e) {
            e.stopPropagation();
        });

        // Prevent dropdown from closing when hovering over it
        userMenu.addEventListener('mouseenter', function() {
            clearTimeout(hoverTimeout);
        });

        userMenu.addEventListener('mouseleave', function() {
            hoverTimeout = setTimeout(function() {
                userMenu.classList.add('hidden');
            }, 300);
        });
    }

    // Mobile menu functionality
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');

    if (mobileMenuButton && mobileMenu) {
        mobileMenuButton.addEventListener('click', function(e) {
            e.stopPropagation();
            mobileMenu.classList.toggle('hidden');
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', function() {
            mobileMenu.classList.add('hidden');
        });

        // Prevent mobile menu from closing when clicking inside it
        mobileMenu.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    }

    // Close menus when window is resized to desktop size
    window.addEventListener('resize', function() {
        if (window.innerWidth >= 768) { // md breakpoint
            if (mobileMenu) {
                mobileMenu.classList.add('hidden');
            }
        }
    });

    // Notifications functionality
    const notificationsButton = document.getElementById('notifications-button');
    const notificationsMenu = document.getElementById('notifications-menu');
    const notificationsContainer = document.getElementById('notifications-container');
    let notificationTimeout;

    if (notificationsButton && notificationsMenu && notificationsContainer) {
        // Click functionality
        notificationsButton.addEventListener('click', function(e) {
            e.stopPropagation();
            notificationsMenu.classList.toggle('hidden');
            if (!notificationsMenu.classList.contains('hidden')) {
                loadNotifications();
            }
        });

        // Close menu when clicking outside
        document.addEventListener('click', function() {
            notificationsMenu.classList.add('hidden');
        });

        // Prevent menu from closing when clicking inside it
        notificationsMenu.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    }

    // Theme toggle in user menu
    const themeToggleMenu = document.getElementById('theme-toggle-menu');
    if (themeToggleMenu) {
        themeToggleMenu.addEventListener('click', function() {
            // Get current theme state
            const isDark = document.documentElement.classList.contains('dark');

            // Toggle theme
            if (isDark) {
                localStorage.setItem('theme', 'light');
                document.documentElement.classList.remove('dark');
                // Update all theme toggle icons
                document.querySelectorAll('[id*="theme-toggle-dark-icon"]').forEach(icon => icon.classList.remove('hidden'));
                document.querySelectorAll('[id*="theme-toggle-light-icon"]').forEach(icon => icon.classList.add('hidden'));
                document.getElementById('theme-toggle-text').textContent = 'Dark Mode';
            } else {
                localStorage.setItem('theme', 'dark');
                document.documentElement.classList.add('dark');
                // Update all theme toggle icons
                document.querySelectorAll('[id*="theme-toggle-light-icon"]').forEach(icon => icon.classList.remove('hidden'));
                document.querySelectorAll('[id*="theme-toggle-dark-icon"]').forEach(icon => icon.classList.add('hidden'));
                document.getElementById('theme-toggle-text').textContent = 'Light Mode';
            }
        });
    }

    // Load notifications function
    function loadNotifications() {
        fetch('/api/notifications/recent')
            .then(response => response.json())
            .then(data => {
                const notificationsList = document.getElementById('notifications-list');
                const notificationBadge = document.getElementById('notification-badge');

                if (data.notifications && data.notifications.length > 0) {
                    notificationsList.innerHTML = data.notifications.map(notification => `
                        <div class="px-4 py-3 hover:bg-surface-100 dark:hover:bg-surface-700 border-b border-surface-200 dark:border-surface-600 cursor-pointer" onclick="markAsRead(${notification.id})">
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0">
                                    ${getNotificationIcon(notification.type)}
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-body-medium font-medium text-surface-900 dark:text-surface-100">${notification.title}</p>
                                    <p class="text-body-medium text-surface-500 dark:text-surface-400">${notification.message}</p>
                                    <p class="text-body-small text-surface-400 dark:text-surface-500 mt-1">${formatTime(notification.created_at)}</p>
                                </div>
                            </div>
                        </div>
                    `).join('');

                    // Update badge
                    const unreadCount = data.notifications.filter(n => !n.read).length;
                    if (unreadCount > 0) {
                        notificationBadge.textContent = unreadCount;
                        notificationBadge.classList.remove('hidden');
                    } else {
                        notificationBadge.classList.add('hidden');
                    }
                } else {
                    notificationsList.innerHTML = `
                        <div class="px-4 py-3 text-body-medium text-surface-500 dark:text-surface-400 text-center">
                            No new notifications
                        </div>
                    `;
                    notificationBadge.classList.add('hidden');
                }
            })
            .catch(error => {
                console.error('Error loading notifications:', error);
            });
    }

    function getNotificationIcon(type) {
        switch(type) {
            case 'watering':
                return '<span class="material-icons text-lg text-primary-500">water_drop</span>';
            case 'fertilizing':
                return '<span class="material-icons text-lg text-secondary-500">eco</span>';
            case 'harvest':
                return '<span class="material-icons text-lg text-tertiary-500">agriculture</span>';
            default:
                return '<span class="material-icons text-lg text-surface-500">notifications</span>';
        }
    }

    function formatTime(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;

        if (diff < 60000) return 'Just now';
        if (diff < 3600000) return Math.floor(diff / 60000) + 'm ago';
        if (diff < 86400000) return Math.floor(diff / 3600000) + 'h ago';
        return Math.floor(diff / 86400000) + 'd ago';
    }

    function markAsRead(notificationId) {
        fetch(`/api/notifications/${notificationId}/read`, { method: 'POST' })
            .then(() => loadNotifications())
            .catch(error => console.error('Error marking notification as read:', error));
    }

    // Load notifications on page load
    if (document.getElementById('notifications-button')) {
        loadNotifications();
        // Refresh notifications every 30 seconds
        setInterval(loadNotifications, 30000);
    }

    // Global form submission via Enter key
    document.addEventListener('keypress', function(event) {
        if (event.key === 'Enter' && event.target.tagName === 'INPUT' && event.target.type !== 'textarea') {
            const form = event.target.closest('form');
            if (form) {
                // Check if the form has a submit button
                const submitButton = form.querySelector('button[type="submit"], input[type="submit"]');
                if (submitButton && !submitButton.disabled) {
                    event.preventDefault();
                    submitButton.click();
                }
            }
        }
    });
});
</script>
</body>
</html>
