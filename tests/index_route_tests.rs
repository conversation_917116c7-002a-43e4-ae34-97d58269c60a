use actix_web::{test, web, App, http::StatusCode};
use actix_session::{storage::CookieSessionStore, SessionMiddleware};
use actix_web::cookie::Key;
use garden_planner_web::routes;
use garden_planner_web::DbPool;
use diesel::r2d2::{self, ConnectionManager};
use diesel::SqliteConnection;

fn create_test_pool() -> DbPool {
    let manager = ConnectionManager::<SqliteConnection>::new(":memory:");
    r2d2::Pool::builder()
        .build(manager)
        .expect("Failed to create test pool")
}

#[actix_web::test]
async fn test_index_route_unauthenticated() {
    let pool = create_test_pool();
    let app = test::init_service(
        App::new()
            .app_data(web::Data::new(pool))
            .wrap(SessionMiddleware::new(
                CookieSessionStore::default(),
                Key::generate(),
            ))
            .service(web::scope("").configure(routes::init))
    ).await;

    let req = test::TestRequest::get()
        .uri("/")
        .to_request();
    
    let resp = test::call_service(&app, req).await;
    
    assert_eq!(resp.status(), StatusCode::OK, "Index route should return 200 OK");
    
    let body = test::read_body(resp).await;
    let body_str = std::str::from_utf8(&body).unwrap();
    
    // Verify essential content is present
    assert!(body_str.contains("Welcome to Garden Planner"), "Should contain welcome message");
    assert!(body_str.contains("Get Started"), "Should contain get started button");
    assert!(body_str.contains("Login"), "Should contain login link");
    assert!(body_str.contains("Register"), "Should contain register link");
    assert!(body_str.contains("<!DOCTYPE html>"), "Should be valid HTML");
    assert!(body_str.contains("</html>"), "Should be complete HTML document");
}

#[actix_web::test]
async fn test_index_route_content_structure() {
    let pool = create_test_pool();
    let app = test::init_service(
        App::new()
            .app_data(web::Data::new(pool))
            .wrap(SessionMiddleware::new(
                CookieSessionStore::default(),
                Key::generate(),
            ))
            .service(web::scope("").configure(routes::init))
    ).await;

    let req = test::TestRequest::get()
        .uri("/")
        .to_request();
    
    let resp = test::call_service(&app, req).await;
    let body = test::read_body(resp).await;
    let body_str = std::str::from_utf8(&body).unwrap();
    
    // Verify HTML structure
    assert!(body_str.contains("<html"), "Should contain html tag");
    assert!(body_str.contains("<head>"), "Should contain head section");
    assert!(body_str.contains("<body"), "Should contain body tag");
    assert!(body_str.contains("<nav"), "Should contain navigation");
    assert!(body_str.contains("<main"), "Should contain main content");
    
    // Verify meta tags
    assert!(body_str.contains("charset=\"UTF-8\""), "Should have UTF-8 charset");
    assert!(body_str.contains("viewport"), "Should have viewport meta tag");
    
    // Verify CSS and JS includes
    assert!(body_str.contains("/static/css/style.css"), "Should include main CSS");
    assert!(body_str.contains("/static/js/scripts.js"), "Should include main JS");
    
    // Verify theme toggle functionality
    assert!(body_str.contains("theme-toggle"), "Should include theme toggle");
}

#[actix_web::test]
async fn test_index_route_navigation_links() {
    let app = test::init_service(
        App::new()
            .wrap(SessionMiddleware::new(
                CookieSessionStore::default(),
                Key::generate(),
            ))
            .service(web::scope("").configure(routes::init))
    ).await;

    let req = test::TestRequest::get()
        .uri("/")
        .to_request();

    let resp = test::call_service(&app, req).await;
    let body = test::read_body(resp).await;
    let body_str = std::str::from_utf8(&body).unwrap();

    // Verify navigation links for unauthenticated users
    assert!(body_str.contains("href=\"/auth/register\""), "Should have register link");
    assert!(body_str.contains("href=\"/auth/login\""), "Should have login link");
    assert!(body_str.contains("href=\"/\""), "Should have home link");

    // Should not contain authenticated user links
    assert!(!body_str.contains("href=\"/households\""), "Should not have households link for guests");
    assert!(!body_str.contains("href=\"/plants\""), "Should not have plants link for guests");
    assert!(!body_str.contains("href=\"/seeds\""), "Should not have seeds link for guests");
    assert!(!body_str.contains("Logout"), "Should not have logout link for guests");
}

#[actix_web::test]
async fn test_index_route_features_section() {
    let app = test::init_service(
        App::new()
            .wrap(SessionMiddleware::new(
                CookieSessionStore::default(),
                Key::generate(),
            ))
            .service(web::scope("").configure(routes::init))
    ).await;

    let req = test::TestRequest::get()
        .uri("/")
        .to_request();
    
    let resp = test::call_service(&app, req).await;
    let body = test::read_body(resp).await;
    let body_str = std::str::from_utf8(&body).unwrap();
    
    // Verify features section content
    assert!(body_str.contains("Plant Database"), "Should mention plant database feature");
    assert!(body_str.contains("Season Planning"), "Should mention season planning feature");
    assert!(body_str.contains("Smart Notifications"), "Should mention notifications feature");
    
    // Verify feature descriptions
    assert!(body_str.contains("Comprehensive plant information"), "Should describe plant database");
    assert!(body_str.contains("automated optimization"), "Should describe season planning");
    assert!(body_str.contains("personalized reminders"), "Should describe notifications");
}

#[actix_web::test]
async fn test_index_route_responsive_design() {
    let app = test::init_service(
        App::new()
            .wrap(SessionMiddleware::new(
                CookieSessionStore::default(),
                Key::generate(),
            ))
            .service(web::scope("").configure(routes::init))
    ).await;

    let req = test::TestRequest::get()
        .uri("/")
        .to_request();
    
    let resp = test::call_service(&app, req).await;
    let body = test::read_body(resp).await;
    let body_str = std::str::from_utf8(&body).unwrap();
    
    // Verify responsive design classes
    assert!(body_str.contains("md:"), "Should contain medium breakpoint classes");
    assert!(body_str.contains("hidden md:flex"), "Should have responsive navigation");
    assert!(body_str.contains("md:hidden"), "Should have mobile menu button");
    assert!(body_str.contains("grid-cols-1 md:grid-cols-3"), "Should have responsive grid");
}

#[actix_web::test]
async fn test_index_route_dark_mode_support() {
    let app = test::init_service(
        App::new()
            .wrap(SessionMiddleware::new(
                CookieSessionStore::default(),
                Key::generate(),
            ))
            .service(web::scope("").configure(routes::init))
    ).await;

    let req = test::TestRequest::get()
        .uri("/")
        .to_request();
    
    let resp = test::call_service(&app, req).await;
    let body = test::read_body(resp).await;
    let body_str = std::str::from_utf8(&body).unwrap();
    
    // Verify dark mode classes
    assert!(body_str.contains("dark:"), "Should contain dark mode classes");
    assert!(body_str.contains("dark:bg-sage-950"), "Should have dark background");
    assert!(body_str.contains("dark:text-sage-100"), "Should have dark text colors");
    assert!(body_str.contains("theme-toggle"), "Should have theme toggle button");
}

#[actix_web::test]
async fn test_index_route_accessibility() {
    let app = test::init_service(
        App::new()
            .wrap(SessionMiddleware::new(
                CookieSessionStore::default(),
                Key::generate(),
            ))
            .service(web::scope("").configure(routes::init))
    ).await;

    let req = test::TestRequest::get()
        .uri("/")
        .to_request();
    
    let resp = test::call_service(&app, req).await;
    let body = test::read_body(resp).await;
    let body_str = std::str::from_utf8(&body).unwrap();
    
    // Verify accessibility features
    assert!(body_str.contains("lang=\"en\""), "Should have language attribute");
    assert!(body_str.contains("alt="), "Should have alt attributes for images (if any)");
    
    // Verify semantic HTML
    assert!(body_str.contains("<nav"), "Should use semantic nav element");
    assert!(body_str.contains("<main"), "Should use semantic main element");
    assert!(body_str.contains("<h1"), "Should have proper heading hierarchy");
}

#[actix_web::test]
async fn test_index_route_performance() {
    let app = test::init_service(
        App::new()
            .wrap(SessionMiddleware::new(
                CookieSessionStore::default(),
                Key::generate(),
            ))
            .service(web::scope("").configure(routes::init))
    ).await;

    let start = std::time::Instant::now();
    
    let req = test::TestRequest::get()
        .uri("/")
        .to_request();
    
    let resp = test::call_service(&app, req).await;
    
    let duration = start.elapsed();
    
    assert_eq!(resp.status(), StatusCode::OK);
    assert!(duration.as_millis() < 1000, "Index route should respond within 1 second");
    
    let body = test::read_body(resp).await;
    assert!(!body.is_empty(), "Response should not be empty");
}

#[actix_web::test]
async fn test_index_route_multiple_requests() {
    let app = test::init_service(
        App::new()
            .wrap(SessionMiddleware::new(
                CookieSessionStore::default(),
                Key::generate(),
            ))
            .service(web::scope("").configure(routes::init))
    ).await;

    // Test multiple concurrent requests
    let mut handles = vec![];
    
    for _ in 0..5 {
        let app_clone = &app;
        handles.push(async move {
            let req = test::TestRequest::get()
                .uri("/")
                .to_request();
            
            let resp = test::call_service(app_clone, req).await;
            assert_eq!(resp.status(), StatusCode::OK);
            
            let body = test::read_body(resp).await;
            let body_str = std::str::from_utf8(&body).unwrap();
            assert!(body_str.contains("Welcome to Garden Planner"));
        });
    }
    
    // Wait for all requests to complete
    for handle in handles {
        handle.await;
    }
}

#[actix_web::test]
async fn test_index_route_headers() {
    let app = test::init_service(
        App::new()
            .wrap(SessionMiddleware::new(
                CookieSessionStore::default(),
                Key::generate(),
            ))
            .service(web::scope("").configure(routes::init))
    ).await;

    let req = test::TestRequest::get()
        .uri("/")
        .to_request();
    
    let resp = test::call_service(&app, req).await;
    
    // Verify response headers
    assert_eq!(resp.status(), StatusCode::OK);
    
    let headers = resp.headers();
    assert!(headers.contains_key("content-type"), "Should have content-type header");
    
    let content_type = headers.get("content-type").unwrap().to_str().unwrap();
    assert!(content_type.contains("text/html"), "Should be HTML content type");
}
