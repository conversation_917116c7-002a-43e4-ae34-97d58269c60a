use actix_session::Session;
use actix_web::{web, HttpResponse, Result, HttpRequest, ResponseError};
use serde::Deserialize;
use diesel::prelude::*;
use crate::models::plant::{NewPlant, Plant};
use crate::models::wishlist::{Wishlist, NewWishlist};
use crate::utils::templates::render_template_with_context;
use crate::DbPool;
use crate::services::plant_service::PlantService;
use crate::utils::error::AppError;
use crate::services::herba_gatherer::{HerbaGatherer, PlantIdentifier};
use crate::schema::wishlists;

#[derive(Deserialize)]
pub struct PlantForm {
    pub name: String,
    pub description: Option<String>,
    pub latin_name: Option<String>,
    pub variety: Option<String>,
    pub note: Option<String>,
    pub nutrient_consumption: Option<String>,
    pub nutrient_deposit: Option<String>,
    pub lighting: Option<String>,
    pub temperature: Option<String>,
    pub light_amount: Option<String>,
    pub sowing_time: Option<String>,
    pub propagation_time: Option<String>,
    pub harvest_time: Option<String>,
    pub growth_duration: Option<String>,
    pub harvest_duration: Option<String>,
    pub field_id: Option<i32>,
}

#[derive(Deserialize)]
pub struct BulkActionForm {
    pub plant_ids: Vec<i32>,
}



pub async fn list_plants(session: Session, pool: web::Data<DbPool>) -> Result<HttpResponse> {
    if !crate::utils::auth::is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let all_plants = match PlantService::get_all_plants(&pool) {
        Ok(plants) => plants,
        Err(e) => {
            log::error!("Error loading plants: {}", e);
            return Ok(e.error_response());
        }
    };

    // Calculate statistics for template
    let plants_with_latin_names = all_plants.iter().filter(|p| p.latin_name.is_some()).count();
    let plants_with_varieties = all_plants.iter().filter(|p| p.variety.is_some()).count();

    let mut ctx = tera::Context::new();
    ctx.insert("plants", &all_plants);
    ctx.insert("plants_with_latin_names", &plants_with_latin_names);
    ctx.insert("plants_with_varieties", &plants_with_varieties);

    Ok(render_template_with_context("plants/list.html", &mut ctx, &session)?)
}

pub async fn create_plant(
    session: Session,
    req: HttpRequest,
    form_data: web::Form<PlantForm>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if session.get::<String>("username")?.is_some() {
        let new_plant = NewPlant {
            name: &form_data.name,
            description: form_data.description.as_deref(),
            latin_name: form_data.latin_name.as_deref(),
            variety: form_data.variety.as_deref(),
            note: form_data.note.as_deref(),
            nutrient_consumption: form_data.nutrient_consumption.as_deref(),
            nutrient_deposit: form_data.nutrient_deposit.as_deref(),
            lighting: form_data.lighting.as_deref(),
            temperature: form_data.temperature.as_deref(),
            light_amount: form_data.light_amount.as_deref(),
            sowing_time: form_data.sowing_time.as_deref(),
            propagation_time: form_data.propagation_time.as_deref(),
            harvest_time: form_data.harvest_time.as_deref(),
            growth_duration: form_data.growth_duration.as_deref(),
            harvest_duration: form_data.harvest_duration.as_deref(),
            field_id: form_data.field_id,
            herba_plant_id: None, // Will be set later by herba auto-gathering
        };

        let inserted_plant = match PlantService::create_plant(&pool, &new_plant) {
            Ok(plant) => plant,
            Err(e) => {
                log::error!("Error creating plant: {}", e);
                return Ok(e.error_response());
            }
        };

        // Check if the request is an AJAX request
        if req.headers().get("X-Requested-With").map_or(false, |h| h == "XMLHttpRequest") {
            // Return JSON response
            Ok(HttpResponse::Ok().json(serde_json::json!({
                "success": true,
                "plant": {
                    "id": inserted_plant.id,
                    "name": inserted_plant.name,
                    "latin_name": inserted_plant.latin_name,
                    "field_id": inserted_plant.field_id,
                    // Include other fields as necessary
                }
            })))
        } else {
            // Redirect for normal form submissions
            Ok(HttpResponse::Found()
                .append_header(("Location", "/plants/list"))
                .finish())
        }
    } else {
        Ok(HttpResponse::Unauthorized().finish())
    }
}

pub async fn edit_plant_form(
    session: Session,
    plant_id: web::Path<i32>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse> {
    if session.get::<String>("username")?.is_some() {
        let plant = match PlantService::get_plant_by_id(&pool, *plant_id) {
            Ok(Some(plant)) => plant,
            Ok(None) => {
                let error = AppError::NotFoundError(format!("Plant with ID {} not found", *plant_id));
                return Ok(error.error_response());
            },
            Err(e) => {
                log::error!("Error loading plant: {}", e);
                return Ok(e.error_response());
            }
        };

        let mut ctx = tera::Context::new();
        ctx.insert("plant", &plant);
        crate::utils::templates::render_template_with_context("plants/edit.html", &mut ctx, &session)
    } else {
        Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish())
    }
}


pub async fn update_plant(
    session: Session,
    plant_id: web::Path<i32>,
    form: web::Form<PlantForm>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse> {
    if session.get::<String>("username")?.is_some() {
        let updated_plant = Plant {
            id: *plant_id,
            name: form.name.clone(),
            description: form.description.clone(),
            latin_name: form.latin_name.clone(),
            variety: form.variety.clone(),
            note: form.note.clone(),
            nutrient_consumption: form.nutrient_consumption.clone(),
            nutrient_deposit: form.nutrient_deposit.clone(),
            lighting: form.lighting.clone(),
            temperature: form.temperature.clone(),
            light_amount: form.light_amount.clone(),
            sowing_time: form.sowing_time.clone(),
            propagation_time: form.propagation_time.clone(),
            harvest_time: form.harvest_time.clone(),
            growth_duration: form.growth_duration.clone(),
            harvest_duration: form.harvest_duration.clone(),
            field_id: form.field_id,
            herba_plant_id: None, // Keep existing herba_plant_id or update via service
        };

        if let Err(e) = PlantService::update_plant(&pool, *plant_id, &updated_plant) {
            log::error!("Error updating plant: {}", e);
            return Ok(e.error_response());
        }

        Ok(HttpResponse::Found()
            .append_header(("Location", "/plants/list"))
            .finish())
    } else {
        Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish())
    }
}

pub async fn new_plant_form(session: Session) -> Result<HttpResponse, actix_web::Error> {
    if !crate::utils::auth::is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let mut ctx = tera::Context::new();
    crate::utils::templates::render_template_with_context("plants/new.html", &mut ctx, &session)
}


pub async fn delete_plant(
    session: Session,
    plant_id: web::Path<i32>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse> {
    if session.get::<String>("username")?.is_some() {
        if let Err(e) = PlantService::delete_plant(&pool, *plant_id) {
            log::error!("Error deleting plant: {}", e);
            return Ok(e.error_response());
        }

        Ok(HttpResponse::Found()
            .append_header(("Location", "/plants/list"))
            .finish())
    } else {
        Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish())
    }
}


pub async fn gather_herba_data(
    session: Session,
    plant_id: web::Path<i32>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse> {
    if !crate::utils::auth::is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    // Get the plant
    let plant = match PlantService::get_plant_by_id(&pool, *plant_id) {
        Ok(Some(plant)) => plant,
        Ok(None) => {
            let error = AppError::NotFoundError(format!("Plant with ID {} not found", *plant_id));
            return Ok(error.error_response());
        },
        Err(e) => {
            log::error!("Error loading plant: {}", e);
            return Ok(e.error_response());
        }
    };

    // Create plant identifier
    let identifier = PlantIdentifier {
        latin_name: plant.latin_name.clone(),
        common_name: Some(plant.name.clone()),
        partial_name: None,
        family: None,
        genus: None,
    };

    // Gather herba data
    let gatherer = HerbaGatherer::new();
    match gatherer.gather_plant_info(identifier).await {
        Ok(gathered_data) => {
            // Save to herba database
            match gatherer.save_to_database(gathered_data, &mut conn).await {
                Ok(herba_plant) => {
                    // Update the plant with herba_plant_id
                    let updated_plant = Plant {
                        id: plant.id,
                        name: plant.name,
                        description: plant.description,
                        latin_name: plant.latin_name,
                        variety: plant.variety,
                        note: plant.note,
                        nutrient_consumption: plant.nutrient_consumption,
                        nutrient_deposit: plant.nutrient_deposit,
                        lighting: plant.lighting,
                        temperature: plant.temperature,
                        light_amount: plant.light_amount,
                        sowing_time: plant.sowing_time,
                        propagation_time: plant.propagation_time,
                        harvest_time: plant.harvest_time,
                        growth_duration: plant.growth_duration,
                        harvest_duration: plant.harvest_duration,
                        field_id: plant.field_id,
                        herba_plant_id: Some(herba_plant.id),
                    };

                    if let Err(e) = PlantService::update_plant(&pool, plant.id, &updated_plant) {
                        log::error!("Error updating plant with herba data: {}", e);
                    }

                    Ok(HttpResponse::Found()
                        .append_header(("Location", format!("/plants/{}/edit", plant.id)))
                        .finish())
                },
                Err(e) => {
                    log::error!("Error saving herba data: {}", e);
                    Ok(HttpResponse::InternalServerError().body("Failed to save herba data"))
                }
            }
        },
        Err(e) => {
            log::error!("Error gathering herba data: {}", e);
            Ok(HttpResponse::InternalServerError().body("Failed to gather herba data"))
        }
    }
}

pub async fn auto_populate_plant_data(
    session: Session,
    query: web::Query<std::collections::HashMap<String, String>>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse> {
    if !crate::utils::auth::is_authenticated(&session) {
        return Ok(HttpResponse::Unauthorized().json(serde_json::json!({
            "success": false,
            "message": "Authentication required"
        })));
    }

    let plant_name = match query.get("name") {
        Some(name) => name,
        None => {
            return Ok(HttpResponse::BadRequest().json(serde_json::json!({
                "success": false,
                "message": "Plant name is required"
            })));
        }
    };

    let mut conn = pool.get().expect("Couldn't get DB connection from pool");
    let gatherer = HerbaGatherer::new();

    let identifier = PlantIdentifier {
        latin_name: query.get("latin_name").cloned(),
        common_name: Some(plant_name.clone()),
        partial_name: Some(plant_name.clone()),
        family: None,
        genus: None,
    };

    match gatherer.gather_plant_info(identifier).await {
        Ok(gathered_data) => {
            // Save to herba database
            match gatherer.save_to_database(gathered_data.clone(), &mut conn).await {
                Ok(herba_plant) => {
                    Ok(HttpResponse::Ok().json(serde_json::json!({
                        "success": true,
                        "herba_plant": herba_plant,
                        "gathered_data": gathered_data,
                        "message": "Plant data auto-populated successfully"
                    })))
                }
                Err(e) => {
                    eprintln!("Error saving herba data: {}", e);
                    Ok(HttpResponse::Ok().json(serde_json::json!({
                        "success": true,
                        "gathered_data": gathered_data,
                        "message": "Plant data gathered but not saved to herba database"
                    })))
                }
            }
        }
        Err(e) => {
            eprintln!("Error gathering plant data: {}", e);
            Ok(HttpResponse::InternalServerError().json(serde_json::json!({
                "success": false,
                "message": "Failed to gather plant information"
            })))
        }
    }
}

/// Bulk delete plants
pub async fn bulk_delete_plants(
    session: Session,
    form: web::Form<BulkActionForm>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse> {
    if !crate::utils::auth::is_authenticated(&session) {
        return Ok(HttpResponse::Unauthorized().json(serde_json::json!({
            "error": "Not authenticated"
        })));
    }

    let _conn = pool.get().expect("Couldn't get DB connection from pool");
    let mut deleted_count = 0;

    for plant_id in &form.plant_ids {
        if let Ok(_) = PlantService::delete_plant(&pool, *plant_id) {
            deleted_count += 1;
        }
    }

    Ok(HttpResponse::Ok().json(serde_json::json!({
        "success": true,
        "message": format!("Successfully deleted {} plants", deleted_count)
    })))
}

/// Bulk add plants to wishlist
pub async fn bulk_add_to_wishlist(
    session: Session,
    form: web::Form<BulkActionForm>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse> {
    if !crate::utils::auth::is_authenticated(&session) {
        return Ok(HttpResponse::Unauthorized().json(serde_json::json!({
            "error": "Not authenticated"
        })));
    }

    let user_id = session.get::<i32>("user_id")?.unwrap_or(0);
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");
    let mut added_count = 0;

    for plant_id in &form.plant_ids {
        // Check if item already exists in wishlist
        let exists = Wishlist::exists(&mut conn, user_id, "plant", *plant_id)
            .unwrap_or(false);

        if !exists {
            let new_wishlist_item = NewWishlist {
                user_id,
                item_type: "plant",
                item_id: *plant_id,
                notes: None,
                priority: Some(1), // Default to low priority
            };

            if diesel::insert_into(wishlists::table)
                .values(&new_wishlist_item)
                .execute(&mut conn)
                .is_ok() {
                added_count += 1;
            }
        }
    }

    Ok(HttpResponse::Ok().json(serde_json::json!({
        "success": true,
        "message": format!("Successfully added {} plants to wishlist", added_count)
    })))
}

pub fn init(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/plants")
            .route("/list", web::get().to(list_plants))
            .route("/new", web::get().to(new_plant_form))
            .route("/create", web::post().to(create_plant))
            .route("/{id}/edit", web::get().to(edit_plant_form))
            .route("/{id}/update", web::post().to(update_plant))
            .route("/{id}/delete", web::post().to(delete_plant))
            .route("/{id}/gather_herba", web::post().to(gather_herba_data))
            .route("/auto-populate", web::get().to(auto_populate_plant_data))
            .route("/bulk-delete", web::post().to(bulk_delete_plants))
            .route("/bulk-add-to-wishlist", web::post().to(bulk_add_to_wishlist)),
    );
}
