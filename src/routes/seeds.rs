use actix_session::Session;
use actix_web::{web, HttpResponse, Result};
use diesel::prelude::*;
use serde::Deserialize;
use chrono::Datelike;

use crate::models::seed::{NewSeed, Seed};
use crate::models::wishlist::{Wishlist, NewWishlist};
use crate::schema::seeds::dsl::*;
use crate::schema::plants::dsl as plants_dsl;
use crate::schema::{wishlists};
use crate::utils::templates::render_template;
use crate::schema::seeds::dsl as seeds_dsl;
use crate::DbPool;

#[derive(Deserialize)]
pub struct SeedForm {
    pub name: String,
    pub note: Option<String>,
    pub origin: Option<String>,
    pub acquisition_year: i32,
    pub expiration_year: i32,
    pub herba_id: i32,
}

#[derive(Deserialize)]
pub struct BulkActionForm {
    pub seed_ids: Vec<i32>,
}


pub async fn list_seeds(session: Session, pool: web::Data<DbPool>) -> Result<HttpResponse> {
    if !crate::utils::auth::is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let username = session.get::<String>("username")?.unwrap_or_default();
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    let all_seeds = seeds_dsl::seeds
        .load::<Seed>(&mut conn)
        .expect("Error loading seeds");

    // Calculate statistics for template
    let current_year = 2024; // You might want to get this dynamically
    let fresh_seeds_count = all_seeds.iter().filter(|s| s.expiration_year > current_year).count();
    let expiring_soon_count = all_seeds.iter().filter(|s| s.expiration_year == current_year + 1).count();
    let expired_seeds_count = all_seeds.iter().filter(|s| s.expiration_year < current_year).count();

    let mut ctx = tera::Context::new();
    ctx.insert("seeds", &all_seeds);
    ctx.insert("username", &username);
    ctx.insert("fresh_seeds_count", &fresh_seeds_count);
    ctx.insert("expiring_soon_count", &expiring_soon_count);
    ctx.insert("expired_seeds_count", &expired_seeds_count);

    Ok(render_template("seeds/list.html", &ctx)?)
}

pub async fn new_seed_form(session: Session, pool: web::Data<DbPool>) -> Result<HttpResponse> {
    if !crate::utils::auth::is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    let all_plants = plants_dsl::plants
        .order(plants_dsl::name.asc())
        .load::<crate::models::plant::Plant>(&mut conn)
        .expect("Error loading plants");

    let mut ctx = tera::Context::new();
    ctx.insert("plants", &all_plants);

    crate::utils::templates::render_template_with_context("seeds/new.html", &mut ctx, &session)
}

pub async fn create_seed(
    session: Session,
    form: web::Form<SeedForm>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse> {
    if !crate::utils::auth::is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    let new_seed = NewSeed {
        name: &form.name,
        note: form.note.as_deref(),
        origin: form.origin.as_deref(),
        acquisition_year: form.acquisition_year,
        expiration_year: form.expiration_year,
        herba_id: form.herba_id, // Use herba_id
    };

    diesel::insert_into(seeds_dsl::seeds)
        .values(&new_seed)
        .execute(&mut conn)
        .expect("Error inserting new seed");

    Ok(HttpResponse::Found()
        .append_header(("Location", "/seeds/list"))
        .finish())
}

pub async fn edit_seed_form(
    session: Session,
    seed_id: web::Path<i32>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse> {
    if !crate::utils::auth::is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    let seed = seeds
        .find(*seed_id)
        .first::<Seed>(&mut conn)
        .optional()
        .expect("Error loading seed");

    if let Some(seed) = seed {
        let mut ctx = tera::Context::new();
        ctx.insert("seed", &seed);
        crate::utils::templates::render_template_with_context("seeds/edit.html", &mut ctx, &session)
    } else {
        Ok(HttpResponse::NotFound().body("Seed not found"))
    }
}

pub async fn update_seed(
    session: Session,
    seed_id: web::Path<i32>,
    form: web::Form<SeedForm>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse> {
    if !crate::utils::auth::is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    let updated_seed = Seed {
        id: *seed_id,
        name: form.name.clone(),
        note: form.note.clone(),
        origin: form.origin.clone(),
        acquisition_year: form.acquisition_year,
        expiration_year: form.expiration_year,
        herba_id: form.herba_id,
    };

    diesel::update(seeds.find(*seed_id))
        .set(&updated_seed)
        .execute(&mut conn)
        .expect("Error updating seed");

    Ok(HttpResponse::Found()
        .append_header(("Location", "/seeds/list"))
        .finish())
}

pub async fn delete_seed(
    session: Session,
    seed_id: web::Path<i32>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse> {
    if !crate::utils::auth::is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    diesel::delete(seeds.find(*seed_id))
        .execute(&mut conn)
        .expect("Error deleting seed");

    Ok(HttpResponse::Found()
        .append_header(("Location", "/seeds/list"))
        .finish())
}

/// Bulk delete seeds
pub async fn bulk_delete_seeds(
    session: Session,
    form: web::Form<BulkActionForm>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse> {
    if !crate::utils::auth::is_authenticated(&session) {
        return Ok(HttpResponse::Unauthorized().json(serde_json::json!({
            "error": "Not authenticated"
        })));
    }

    let mut conn = pool.get().expect("Couldn't get DB connection from pool");
    let mut deleted_count = 0;

    for seed_id in &form.seed_ids {
        if diesel::delete(seeds.find(*seed_id))
            .execute(&mut conn)
            .is_ok() {
            deleted_count += 1;
        }
    }

    Ok(HttpResponse::Ok().json(serde_json::json!({
        "success": true,
        "message": format!("Successfully deleted {} seeds", deleted_count)
    })))
}

/// Bulk add seeds to wishlist
pub async fn bulk_add_seeds_to_wishlist(
    session: Session,
    form: web::Form<BulkActionForm>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse> {
    if !crate::utils::auth::is_authenticated(&session) {
        return Ok(HttpResponse::Unauthorized().json(serde_json::json!({
            "error": "Not authenticated"
        })));
    }

    let user_id = session.get::<i32>("user_id")?.unwrap_or(0);
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");
    let mut added_count = 0;

    for seed_id in &form.seed_ids {
        // Check if item already exists in wishlist
        let exists = Wishlist::exists(&mut conn, user_id, "seed", *seed_id)
            .unwrap_or(false);

        if !exists {
            let new_wishlist_item = NewWishlist {
                user_id,
                item_type: "seed",
                item_id: *seed_id,
                notes: None,
                priority: Some(1), // Default to low priority
            };

            if diesel::insert_into(wishlists::table)
                .values(&new_wishlist_item)
                .execute(&mut conn)
                .is_ok() {
                added_count += 1;
            }
        }
    }

    Ok(HttpResponse::Ok().json(serde_json::json!({
        "success": true,
        "message": format!("Successfully added {} seeds to wishlist", added_count)
    })))
}

pub fn init(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/seeds")
            .route("/list", web::get().to(list_seeds))
            .route("/new", web::get().to(new_seed_form))
            .route("/create", web::post().to(create_seed))
            .route("/{id}/edit", web::get().to(edit_seed_form))
            .route("/{id}/update", web::post().to(update_seed))
            .route("/{id}/delete", web::post().to(delete_seed))
            .route("/bulk-delete", web::post().to(bulk_delete_seeds))
            .route("/bulk-add-to-wishlist", web::post().to(bulk_add_seeds_to_wishlist)),
    );
}
